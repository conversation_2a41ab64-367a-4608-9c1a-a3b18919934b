﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Workers</title>
</head>
<body style="margin: 0; padding: 0; box-sizing: border-box; font-family: arial">
    <div style="max-width: 600px; margin: auto">
        <!-- logo  -->
        <div style="background-color: #eff3f6; height: 100px; text-align: center; line-height: 100px;">
            <img src="{13}"
                 alt=""
                 style="width: 200px" />
        </div>
        <!-- header  -->
        <h2 style="background-color: #008284; height: 100px; color: white; font-family: Arial, sans-serif; margin: 0; text-align: center; line-height: 100px; border: 1px solid black">
            New Request: #{0}
        </h2>
        <div style="padding-top: 10px">
            <p style="font-weight: bold; padding-left: 20px">Hello, {1},</p>
            <p style="font-weight: bold; padding-left: 20px">
                This is the required request.
            </p>
            <!-- separator  -->
            <div style="background-color: #008284; height: 3px"></div>
            <table style="padding: 5px 0; width: 100%">
                <tr>
                    <td style="width: 25%"></td>
                    <td style="width: 35%; font-weight: bold">Category:</td>
                    <td style="width: 35%; font-weight: bold;color:darkblue">{2}</td>
                </tr>
                <tr>
                    <td></td>
                    <td style="font-weight: bold">Execution Date:</td>
                    <td style="color: darkblue">{3}</td>
                </tr>
            </table>
            <!-- separator  -->
            <div style="background-color: #008284; height: 3px"></div>

            <table style="margin: 20px 0; width: 100%;">
                <tbody>
                    <tr>
                        <td style="font-weight: bold; width: 10%; vertical-align: top">
                            Customer:
                        </td>
                        <td style="vertical-align: top;width: 35%">
                            <p style="margin: 0; color: darkblue">
                                {4}<br />
                                {5}<br />
                                {6}
                            </p>
                            <p style="margin: 0; margin-top: 15px; color: darkblue">
                                {7}, <br />
                                {8}
                            </p>
                        </td>
                        <td style="font-weight: bold; vertical-align: top;width: 10%">Object:</td>
                        <td style="vertical-align: top; color: darkblue">
                            {9}
                        </td>
                    </tr>
                </tbody>
            </table>
            <!-- separator  -->
            <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

            <table style="width: 100%; margin: 20px 0">
                <tbody>
                    <tr>
                        <td style="font-weight: bold;width: 30%;">Additional Services:</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td style="font-weight: bold;width: 20%;">Flexibility:</td>
                        <td style="padding-left: 10px; color: darkblue">{10}</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td style="font-weight: bold">Customer Focus:</td>
                        <td style="padding-left: 10px; color: darkblue">{11}</td>
                    </tr>
                </tbody>
            </table>

            {14}

            <!-- separator  -->
            <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

            <p style="font-weight: bold">
                If you wish to purchase this order and need detailed information, please click here.
            </p>
            <a href="{12}"
               style="display: block; background-color: #008284; color: white; width: 25%; margin: 20px auto 20px; font-size: 25px; border: none; font-weight: 600; letter-spacing: 1px; text-decoration: none; text-align: center; padding: 10px 20px; border: 1px solid black">
                Details
            </a>
        </div>
        <!-- separator  -->
        <div style="background-color: #008284; height: 3px; margin: auto"></div>
        <!-- footer  -->
        <footer style="font-weight: 600">
            <p>
                Please contact the client immediately to either schedule a visit or prepare a price quote.
            </p>
            <p style="margin-top: 20px; margin-bottom: 20px">
                Thank you and kind regards,
            </p>
            <p style="font-weight: 800">Your TaskDotNet Team</p>
        </footer>
    </div>
</body>
</html>
