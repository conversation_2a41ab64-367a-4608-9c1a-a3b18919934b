﻿using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Models;
using Admin.TaskDotNet.Dtos;
using TaskDotNet.Helper;
using AutoMapper;
using Comman.Services.Interfaces;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.Globalization;
using Comman.Helper.Extensions;

namespace TaskDotNet.Api_s.Services
{
    public class MovingAndCleaningApiService : IMovingAndCleaningApiService
    {
        private readonly ApplicationDbContext context;
        private readonly IMapper _mapper;
        private readonly IMailService _mailService;
        private readonly IActivityService _activityService;
        private readonly OrderNumberService _orderNumberService;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;

        public MovingAndCleaningApiService(IMapper mapper, IMailService mailService,
                             IActivityService activityService, ApplicationDbContext context, IEmailHtmlTemplateService emailHtmlTemplateService, IStringLocalizer<SharedResource> sharedLocalizer)
        {
            _mapper = mapper;
            _mailService = mailService;
            _activityService = activityService;
            _orderNumberService = new OrderNumberService();
            this.context = context;
            _emailHtmlTemplateService = emailHtmlTemplateService;
            SharedLocalizer = sharedLocalizer;
        }

        public async Task<Activity> AddInventoryToMovingAndCleaningAsync(AddInventoryWithMovingAndCleaningRequest request)
        {
            var companyData = context.Company.Select(m=>new { m.Offers_MovClean,m.Price_MovClean }).FirstOrDefault(x => true);

            var activity = _mapper.Map<Activity>(request.MovingDto);

            var inventoryItems = _mapper.Map<List<ActivityInventoryItem>>(request.InventoryItems);

            activity.ActivityType = ActivityType.MovingAndCleaning;
            activity.InventoryItems = inventoryItems;
            activity.Preis = companyData.Price_MovClean;
            activity.PaymentCount = companyData.Offers_MovClean;
            activity.Source = "TaskDotNet";

            activity = await Create(activity);

            activity.OrderNr = _orderNumberService.GenerateOrderNumber(activity.ActivityType, activity.Id);
            await Update(activity);

            request.MovingDto.OrderNr = activity.OrderNr;
            await SendThankYouEmailAsync(activity, request.Lang);

            string inventoryHtml = GetInventoryHtml(inventoryItems, request.Lang);
            await NotifyPartnersAsync(activity, request.Lang, request.MovingDto, inventoryHtml);

            return activity;
        }

        public async Task<Activity> Create(Activity activity)
        {
            await context.Activities.AddAsync(activity);
            context.SaveChanges();
            return activity;
        }

        public async Task<Activity> CreateMovingAndCleaningActivityAsync(MovingCleaningDto dto, string lang)
        {
            var companyData = context.Company.Select(m => new { m.Offers_MovClean, m.Price_MovClean }).FirstOrDefault(x => true);

            var data = _mapper.Map<Activity>(dto);
            data.ActivityType = ActivityType.MovingAndCleaning;
            data.Preis = companyData.Price_MovClean;
            data.PaymentCount = companyData.Offers_MovClean;
            data.Source = "TaskDotNet";

            data = await Create(data);
            data.OrderNr = _orderNumberService.GenerateOrderNumber(data.ActivityType, data.Id);
            await Update(data);

            dto.OrderNr = data.OrderNr;

            await SendThankYouEmailAsync(data, lang);
            await NotifyPartnersAsync(data, lang, dto, "");

            return data;
        }

        public async Task Update(Activity activity)
        {
            context.Activities.Update(activity);
            await context.SaveChangesAsync();
        }


        private async Task SendThankYouEmailAsync(Activity data, string lang)
        {
            string body = _emailHtmlTemplateService.GetCustomerThankYouTemplate(data.Salute.GetDisplayName(), data.Name, lang);
            MailRequest mailRequest = new()
            {
                ToEmail = data.Email,
                Subject = "TaskDotNet",
                Body = body
            };
            await _mailService.SendEmailAsync(mailRequest, default);
        }

        private async Task NotifyPartnersAsync(Activity data, string lang, MovingCleaningDto dto, string inventoryHtml)
        {
            var partners = await _activityService.GetPartnersByCountryAndActivity(data.Kanton, data.ActivityType);
            foreach (var partner in partners)
            {
                string requestBody = _emailHtmlTemplateService.GetRequestFrontendMovingAndCleaningTemplate(partner.CompanyName, dto,lang, inventoryHtml);
                var partnerMailRequest = new MailRequest
                {
                    ToEmail = partner.Email,
                    Subject = "TaskDotNet",
                    Body = requestBody
                };
                await _mailService.SendEmailAsync(partnerMailRequest, default);
            }
        }


        private string GetInventoryHtml(List<ActivityInventoryItem> inventoryData, string lang)
        {
            SetCulture(lang);

            StringBuilder servicesHtml = new();

            string furnitureHtml = string.Empty;
            if (inventoryData.Any())
            {
                var table = new StringBuilder();
                table.Append($@"
                    <div class='col'>
                        <h4 style='font-siye=20px;font-weight: 600;text-align: center;text-decoration: underline;'>{SharedLocalizer["Inventory list"]}:</h4>
                        <table class='table table-bordered table-responsive table-striped' style='border-collapse: collapse;width:100%;'>
                            <thead style='background-color: #008284; color:white; font-weight: bolder;'>
                                <tr>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Space"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Items"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Number"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Total volume"]}</th>
                                </tr>
                            </thead>
                            <tbody>
                ");

                decimal totalVolumes = 0;

                var groupedInventory = inventoryData
                    .Where(item => !string.IsNullOrEmpty(item.Category))
                    .GroupBy(item => item.Category)
                    .ToList();

                foreach (var room in groupedInventory)
                {

                    string? roomName = room.Key;

                    var furnitureList = room.ToList();

                    foreach (var furniture in furnitureList)
                    {
                        string? furnitureName = lang switch
                        {
                            "de" => furniture.GermanName,
                            "en" => furniture.EnglishName,
                            "it" => furniture.ItalianName,
                            "fr" => furniture.FrenchName,
                            _ => furniture.EnglishName
                        };

                        var furnitureQuantity = furniture.Count;
                        double furnitureTotalVolumes = furniture.Total ?? 0;

                        table.Append("<tr>");
                        if (furniture == furnitureList.First())
                        {
                            table.Append($"<td rowspan='{furnitureList.Count}' style='border: 1px solid #ddd; padding: 8px;'>{roomName}</td>");
                        }

                        table.Append($@"
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureName}</td>
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureQuantity} {furniture.Unit}</td>
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureTotalVolumes.ToString("0.00")} m3</td>
                        ");

                        totalVolumes += (decimal)furnitureTotalVolumes;

                        table.Append("</tr>");
                    }

                }

                table.Append($@"
                                <tr><td colspan='4' style='border: 1px solid #ddd; padding: 8px;'></td></tr>
                                <tr class='total-weight-row' style='font-weight: bold;'>
                                    <td colspan='3' style='text-align: right; border: 1px solid #ddd; padding: 8px;'>{SharedLocalizer["Total volume"]}</td>
                                    <td style='text-align: center; border: 1px solid #ddd; padding: 8px;'>{totalVolumes.ToString("0.00")} m3</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                ");

                furnitureHtml = table.ToString();
            }
            return furnitureHtml;
        }

        private void SetCulture(string lang)
        {
            try
            {
                var culture = new CultureInfo(lang);
                CultureInfo.CurrentUICulture = culture;
                CultureInfo.CurrentCulture = culture;
            }
            catch (CultureNotFoundException)
            {
                // Log and fallback to a default culture (e.g., English)
                var defaultCulture = new CultureInfo("en");
                CultureInfo.CurrentUICulture = defaultCulture;
                CultureInfo.CurrentCulture = defaultCulture;
            }
        }
    }
}
