import { Stack, Typography } from "@mui/material";
import React from "react";
import { SectionTitle } from "../components/SectionTitle";
import { useTranslation } from "react-i18next";

const CustomLineBreak = ({ height = "20px" }) => (
    <span style={{ display: "block", height: height }}></span>
);

const DatenPage = () => {
    const { t } = useTranslation("daten");
    
    return (
        <Stack
            sx={{
                color: "colors.mainBlue",
                alignItems: "center",
                justifyContent: "center",
                minHeight: "50vh",
                py: "50px",
            }}
        >
            <SectionTitle sx={{ fontFamily: "Inter, Jura, Roboto, sans-serif" }}>{t("title")}</SectionTitle>

            <Typography
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.who.title")}</strong>
                <CustomLineBreak />
                {t("sections.who.content1")}
                <CustomLineBreak />
                {t("sections.who.address")}
                <br />
                {t("sections.who.city")}
                <br />
                {t("sections.who.country")}
                <CustomLineBreak />
                {t("sections.who.email")}: <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">
                    <EMAIL>
                </a>
                <br />
                {t("sections.who.website")}: <a href="https://www.TaskDotNet.com" target="_blank" rel="noopener noreferrer">
                    www.TaskDotNet.com
                </a>
            </Typography>

            <Typography 
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.data.title")}</strong>
                <CustomLineBreak />
                {t("sections.data.content1")}
                <CustomLineBreak />
                {t("sections.data.content2")}
                <ul>
                    {t("sections.data.reasons").map((reason, index) => (
                        <li key={index}>{reason}</li>
                    ))}
                </ul>
                {t("sections.data.content3")}
            </Typography>

            <Typography 
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.storage.title")}</strong>
                <CustomLineBreak />
                {t("sections.storage.content")}
            </Typography>

            <Typography 
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.comments.title")}</strong>
                <CustomLineBreak />
                {t("sections.comments.content1")}
                <CustomLineBreak />
                {t("sections.comments.content2")}
            </Typography>

            <Typography
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.forms.title")}</strong>
                <CustomLineBreak />
                {t("sections.forms.content")}
            </Typography>

            <Typography
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.cookies.title")}</strong>
                <CustomLineBreak />
                {t("sections.cookies.content1")}
            </Typography>

            <Typography
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.webfonts.title")}</strong>
                <CustomLineBreak />
                {t("sections.webfonts.content")}
            </Typography>

            <Typography
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.analytics.title")}</strong>
                <CustomLineBreak />
                {t("sections.analytics.content1")}
            </Typography>

            <Typography
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.changes.title")}</strong>
                <CustomLineBreak />
                {t("sections.changes.content")}
            </Typography>

            <Typography
                sx={{
                    fontSize: { xs: "16px", sm: "23px" },
                    width: "80%",
                    m: "auto",
                    textAlign: "left",
                    mt: "20px",
                }}
            >
                <strong>{t("sections.questions.title")}</strong>
                <CustomLineBreak />
                {t("sections.questions.content")}&nbsp;
                {t("sections.questions.address")},&nbsp; 
                {t("sections.questions.city")},&nbsp;
                {t("sections.questions.country")}
                <br />
                {t("sections.questions.email")}: <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">
                    <EMAIL>
                </a>
            </Typography>
        </Stack>
    );
};

export default DatenPage;
