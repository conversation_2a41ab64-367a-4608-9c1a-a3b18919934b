﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using TaskDotNet.Api_s.Interfaces;
using TaskDotNet.Api_s.Responses;
using TaskDotNet.Api_s.Services;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNetal.Api_s.Models;
using TaskDotNet.Services;
using TaskDotNet.Helper.Extensions;
using Microsoft.EntityFrameworkCore;
using Comman.Helper.Extensions;

namespace TaskDotNet.Api_s
{
    [Route("api/[controller]")]
    [ApiController]
    public class WorkersController : ControllerBase
    {

        #region ctor
        private readonly IMapper mapper;
        private readonly ApplicationDbContext _context;
        private readonly IActivityService activityService;
        private readonly IMailService mailService;
        private readonly IEmailHtmlTemplateService emailHtmlTemplateService;
        private readonly IWorkersApiService gisperService;

        private readonly OrderNumberService _orderNumberService;

        public WorkersController(IMapper mapper, ApplicationDbContext context, IActivityService activityService, IMailService mailService, IEmailHtmlTemplateService emailHtmlTemplateService, IWorkersApiService gisperService)
        {
            this.mapper = mapper;
            this._context = context;
            this.activityService = activityService;
            this.mailService = mailService;
            this.emailHtmlTemplateService = emailHtmlTemplateService;
            this.gisperService = gisperService;
            _orderNumberService = new OrderNumberService();

        }

        #endregion

        #region Create
        [HttpPost(template: "CreateWorkers")]
        public async Task<IActionResult> CreateAsync([FromBody] WorkersDto dto,string lang)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new CustomResponse { Code = "400", Message = "Invalid request data." });
            }

            var data = mapper.Map<Activity>(dto);

            var activityDetails = GetWorkerActivityDetails(data.ActivityType);
            data.Preis = activityDetails.Price;
            data.PaymentCount = activityDetails.PaymentCount;
            data.Source = "TaskDotNet";

            data = await gisperService.Create(data);

            data.OrderNr = _orderNumberService.GenerateOrderNumber(data.ActivityType, data.Id);
            dto.OrderNr = data.OrderNr;
            gisperService.Update(data);

            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");
            ordersCount.T_Workers += 1;
            

            var notification = new ActivityNotification()
            {
                ActivityType = data.ActivityType,
                City = data.City,
                Name = data.Name,
                PostBox = data.PostBox,
                Salute = data.Salute,
                ExcuteDate = data.MovingDate,
            };
            _context.ActivityNotifications.Add(notification);

            await _context.SaveChangesAsync();

            // Prepare the response object
            var response = new ActivityResponse
            {
                Code = "200",
                Status = "Success",
                Message = $"{data.ActivityType.GetDisplayName()} activity created successfully!",
                Data = data
            };

            string body = emailHtmlTemplateService.GetCustomerThankYouTemplate(data.Salute.GetDisplayName(), data.Name, lang);
            var mailRequest = new MailRequest
            {
                ToEmail = dto.Email,
                Subject = "TaskDotNet",
                Body = body
            };
            await mailService.SendEmailAsync(mailRequest, default);

            var partners = await activityService.GetPartnersByCountryAndActivity(data.Kanton, data.ActivityType);

            foreach (var partner in partners)
            {
                string requestBody = emailHtmlTemplateService.GetRequestFrontendWorkersTemplate(partner.CompanyName, dto, lang);
                var partnerMailRequest = new MailRequest
                {
                    ToEmail = partner.Email,
                    Subject = "TaskDotNet",
                    Body = requestBody
                };
                await mailService.SendEmailAsync(partnerMailRequest, default);
            }

            return Ok(response);

        }

        private (decimal Price, int PaymentCount) GetWorkerActivityDetails(ActivityType activityType)
        {
            var companyData = _context.Company.FirstOrDefault(x => true);

            return activityType switch
            {
                ActivityType.SmallTransport => (companyData.Price_Transp, companyData.Offers_Transp),
                ActivityType.FloorAndPanels => (companyData.Price_Floor, companyData.Offers_Floor),
                ActivityType.Roofer => (companyData.Price_Roofer, companyData.Offers_Roofer),
                ActivityType.KitchenConstruction => (companyData.Price_Kitch, companyData.Offers_Kitch),
                ActivityType.Electrician => (companyData.Price_Elect, companyData.Offers_Elect),
                ActivityType.Garden => (companyData.Price_Garden, companyData.Offers_Garden),
                ActivityType.WallsAndCeilings => (companyData.Price_Walls, companyData.Offers_Walls),
                ActivityType.HeatingAndEnergy => (companyData.Price_Heat, companyData.Offers_Heat),
                ActivityType.Plumbing => (companyData.Price_Pluumb, companyData.Offers_Pluumb),
                ActivityType.Locksmith => (companyData.Price_Lock, companyData.Offers_Lock),
                ActivityType.Welder => (companyData.Price_Welder, companyData.Offers_Welder),
                ActivityType.RefrigerationTechnician => (companyData.Price_Refrig, companyData.Offers_Refrig),
                ActivityType.Mechanic => (companyData.Price_Mechanic, companyData.Offers_Mechanic),
                ActivityType.IndividualActivity => (companyData.Price_Individ, companyData.Offers_Individ),
                _ => (0, 0),
            };
        }

        #endregion


    }
}
