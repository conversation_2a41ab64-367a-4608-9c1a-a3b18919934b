﻿@using Comman.Helper.Extensions
@using TaskDotNet.Helper.Extensions
@{
    string currentLanguage = System.Globalization.CultureInfo.CurrentCulture.Name;
}

<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - TaskDotNet</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/Dashboard/assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
          rel="stylesheet" />

    <script>
        const backgroundColor = localStorage.getItem('CustomColorTheme');
        if (backgroundColor) {
            // Update --main-primary-color
            document.documentElement.style.setProperty('--main-primary-color', backgroundColor);

            // Extract RGB values and update --bs-primary-rgb
            const rgbValues = backgroundColor.match(/\d+/g);
            if (rgbValues) {
                document.documentElement.style.setProperty('--bs-primary-rgb', `${rgbValues[0]}, ${rgbValues[1]}, ${rgbValues[2]}`);
            }
        }
    </script>
    <!-- Icons. Uncomment required icon fonts -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/fonts/boxicons.css" />

    <link href="~/dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.css" rel="stylesheet" />
    <!-- Core CSS -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/core.css" class="template-customizer-core-css" />
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="~/Dashboard/assets/css/demo.css" />

     <!-- Sweet Alert-->
    <link href="~/lib/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />

    <!-- Page CSS -->
    <script src="~/Dashboard/assets/vendor/js/helpers.js"></script>


    <link href="~/dashboard/assets/vendor/css/page-auth.css" rel="stylesheet" />
    <link href="~/lib/flatpickr/flatpickr.min.css" rel="stylesheet" />

    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    @await RenderSectionAsync("Links", required: false)

    <style>
        @@media (max-width: 767.98px) {
            .ClockDate {
                display: none;
            }

            .logoImg {
                display: none;
            }
        }

        .theme-colors {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            padding: 0px 25px;
        }

            .theme-colors .color {
                border-radius: 50%;
                height: 60px;
                width: 60px;
                cursor: pointer;
            }

                .theme-colors .color:hover {
                    opacity: .5;
                }
    </style>
</head>

<body>
    <!-- Overlay and loader -->
    <div class="overlay" id="loader">
        <div class="loader-container">
            <div id="loader-content"></div>
        </div>
    </div>


    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
        <div class="layout-container">
            <!-- Menu -->

            <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
                <div class="app-brand demo">
                    <a asp-controller="Home" asp-action="Index" class="app-brand-link">
                        <span class="app-brand-logo demo">
                            <img class="logoImg" style="width:200px" src="~/Dashboard/assets/img/logo.png">
                        </span>
                    </a>

                    <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
                        <i class="bx bx-chevron-left bx-sm align-middle"></i>
                    </a>
                </div>

                <div class="menu-inner-shadow"></div>

                <ul class="menu-inner py-1">
                    <!-- Dashboard -->
                    <li class="menu-item">
                        <a asp-controller="Home" asp-action="Index" class="menu-link">
                            <i class='menu-icon tf-icons bx bx-home'></i>
                            <div data-i18n="Analytics">@SharedLocalizer["Information Sheet"]</div>
                        </a>
                    </li>

                    <!-- Layouts -->

                    <li class="menu-header small text-uppercase">
                        <span class="menu-header-text">@SharedLocalizer["Pages"]</span>
                    </li>

                    <li class="menu-item">
                        <a asp-controller="Partner" asp-action="Index" class="menu-link ">
                            <i class="menu-icon tf-icons bx bx-dock-top"></i>
                            <div data-i18n="Account Settings">@SharedLocalizer["Partner Profile"]</div>
                        </a>

                    </li>

                    <!-- Components -->
                    <!-- Cards -->
                    <li class="menu-item">
                        <a asp-controller="Partner" asp-action="Activities" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-collection"></i>
                            <div data-i18n="Basic">@SharedLocalizer["Activities List"]</div>
                        </a>
                    </li>

                    <li class="menu-item">
                        <a asp-controller="PurchasedActivites" asp-action="Index" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-collection"></i>
                            <div data-i18n="Basic">@SharedLocalizer["Purchased Activites"]</div>
                        </a>
                    </li>

                    <li class="menu-item">
                        <a asp-controller="Partner" asp-action="Statistics" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-collection"></i>
                            <div data-i18n="Basic">@SharedLocalizer["Statistics"]</div>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a asp-controller="Partner" asp-action="RechargeCredit" class="menu-link">
                            <i class="flex-shrink-0 bx bx-credit-card me-2"></i>
                            <div data-i18n="Basic">@SharedLocalizer["Top Up Balance"] </div>
                        </a>
                    </li>

                    <li class="menu-item text-center" style="margin-top: 9rem;">
                        <img src="~/dashboard/assets/img/partner.png" alt="side view" />
                    </li>
                </ul>
            </aside>
            <!-- / Menu -->
            <!-- Layout container -->
            <div class="layout-page bg-primary">
                <!-- Navbar -->

                <nav class="layout-navbar mb-3 navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme"
                     id="layout-navbar">
                    <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
                        <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
                            <i class="bx bx-menu bx-sm"></i>
                        </a>
                    </div>

                    <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">

                        <div class="nav-item lh-1 me-3">
                            <div class="companyName">
                                <span class="app-brand-logo demo fs-4">
                                    @await Component.InvokeAsync("CompanyName")
                                </span>
                            </div>
                        </div>

                        <div class="ClockDate navbar-nav align-items-center mx-auto">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#colorsModal">
                                <img src="~/dashboard/assets/img/color.png" /> &nbsp; @SharedLocalizer["Background"]
                            </button>

                            <div class="nav-item d-flex align-items-center mx-2">
                                <i class='bx bxs-calendar mx-2' style="font-size:25px"></i>
                                <span id="date" style="font-size:20px"></span>
                                <i class='bx bxs-time mx-2' style="font-size:25px"></i>
                                <span id="clock" style="font-size:20px"></span>
                            </div>

                        </div>

                        <ul class="navbar-nav flex-row align-items-center">


                            <li class="nav-item d-flex navbar-dropdown dropdown-user dropdown mx-4">
                                <a class="nav-link dropdown-toggle hide-arrow  d-flex justify-content-between" data-bs-toggle="dropdown"
                                   href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "de-DE",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">

                                    @if (currentLanguage == "de-DE")
                                    {
                                        <img src="~/Dashboard/assets/img/de-flag.png" width="35" height="35" class="mt-1 mx-2" />

                                    }
                                    @if (currentLanguage == "en-US")
                                    {
                                        <img src="~/Dashboard/assets/img/us-flag.png" width="35" height="35" class="mt-1 mx-2" />

                                    }
                                    @if (currentLanguage == "it-IT")
                                    {
                                        <img src="~/Dashboard/assets/img/it-flag.png" width="35" height="35" class="mt-1 mx-2" />

                                    }
                                    @if (currentLanguage == "fr-FR")
                                    {
                                        <img src="~/Dashboard/assets/img/fr-flag.png" width="35" height="35" class="mt-1 mx-2" />

                                    }
                                    <span class="lang mt-2">@SharedLocalizer["Language"] </span>
                                </a>

                                <ul class="dropdown-menu dropdown-menu-end">

                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "de-DE",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/de-flag.png" width="25" height="25" class="mt-1" />
                                            <span>German</span>
                                        </a>

                                    </li>

                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>

                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "en-US",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/us-flag.png" width="25" height="25" class="mt-1" />
                                            <span> English</span>
                                        </a>
                                    </li>
                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>
                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "it-IT",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/it-flag.png" width="25" height="25" class="mt-1" />
                                            <span>Italy</span>
                                        </a>

                                    </li>

                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>

                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "fr-FR",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/fr-flag.png" width="25" height="25" class="mt-1" />
                                            <span>France</span>
                                        </a>

                                    </li>
                                </ul>
                            </li>

                            <li class="nav-item">
                            </li>

                            <!-- User -->
                            <li class="nav-item navbar-dropdown dropdown-user dropdown">
                                <a class="nav-link dropdown-toggle hide-arrow" data-bs-toggle="dropdown" href="#">
                                    <div class="avatar avatar-online">
                                        <img src="~/Dashboard/assets/img/avatars/1.png" alt="" class="w-px-40 h-auto rounded-circle" />
                                    </div>

                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">


                                    <li>

                                        <a class="dropdown-item" href="#">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0 me-3">
                                                    <div class="avatar avatar-online">
                                                        <img src="~/Dashboard/assets/img/avatars/1.png" alt="" class="w-px-40 h-auto rounded-circle" />
                                                    </div>
                                                </div>
                                                <div class=" flex-grow-1">
                                                    @if (User!.Identity!.IsAuthenticated)
                                                    {
                                                        <div class="fw-semibold ">

                                                            <div class="user-name">@User.Identity.Name</div>

                                                        </div>
                                                    }
                                                    <small class="text-muted">Partner</small>

                                                </div>
                                            </div>
                                        </a>
                                    </li>

                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>


                                    <li>
                                        <a class="dropdown-item" asp-controller="Account" asp-action="Manage">
                                            <i class='bx bx-lock-open-alt me-2'></i>
                                            <span class="align-middle"> @SharedLocalizer["Change password"]</span>
                                        </a>
                                    </li>

                                    <li>
                                        <a class="dropdown-item" asp-controller="Partner" asp-action="Index">
                                            <i class="bx bx-cog me-2"></i>
                                            <span class="align-middle"> @SharedLocalizer["Edit Profile Data"]</span>
                                        </a>
                                    </li>

                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>

                                    <li>
                                        <a class="dropdown-item">
                                            <form asp-page="/Account/Logout" method="post">
                                                <i class="bx bx-power-off me-2"></i>
                                                <input type="submit" class="align-middle" style="border:none; background:none" value="@SharedLocalizer["Log Out"] " />
                                            </form>

                                        </a>
                                    </li>
                                </ul>
                            </li>
                           
                        </ul>
                    </div>


                </nav>
                <!-- Notification Sound -->
                <audio id="notificationSound" src="notification.wav" preload="auto" hidden></audio>

                <!-- / Navbar -->
                <!-- Overlay -->
                @RenderBody()
                <div class="layout-overlay layout-menu-toggle"></div>

                <!-- Notifications Container -->
                <div id="notificationsContainer"></div>
            </div>
            <!-- / Layout wrapper -->


            <!-- Modal -->
            <div class="modal fade" id="colorsModal" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">Wähle eine Farbe</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="theme-colors">
                                @foreach (WebsiteCustomColors color in Enum.GetValues(typeof(WebsiteCustomColors)))
                                {
                                    <div class="color" style="background-color:@color.GetDisplayName()"></div>
                                }
                            </div>
                        </div>

                    </div>
                </div>
            </div>


        </div>
    </div>
    <script src="~/Dashboard/assets/vendor/jquery/jquery.js"></script>
    <script src="~/Dashboard/assets/vendor/popper/popper.js"></script>
    <script src="~/Dashboard/assets/vendor/js/bootstrap.js"></script>
        <!-- Sweet Alerts  -->
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="~/lib/sweetalert2/sweet-alerts.init.js"></script>
    <partial name="~/Views/Shared/_Notification.cshtml" />
    <script>
        (function () {

            // Select all menu items that are anchor tags within .menu-item
            $('.menu-item a').each(function () {
                // Check if the current URL matches the href of the anchor tag
                if (this.href === window.location.href) {
                    // Add 'active' class to the parent <li> element (menu-item)
                    $(this).parent().addClass('active');
                }
            });

            // Listen for color changes
            $('.theme-colors .color').click(function () {
                const backgroundColor = $(this).css('background-color');

                // Update main color and bs-primary-rgb
                $(':root').css('--main-primary-color', backgroundColor);
                localStorage.setItem('CustomColorTheme', backgroundColor);

                const rgbValues = backgroundColor.match(/\d+/g);
                if (rgbValues) {
                    $(':root').css('--bs-primary-rgb', `${rgbValues[0]}, ${rgbValues[1]}, ${rgbValues[2]}`);
                }
            });
        })();
    </script>
    <script src="~/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js"></script>

    <script src="~/Dashboard/assets/vendor/js/menu.js"></script>
    <script src="~/lib/flatpickr/flatpickr.min.js"></script>

    <partial name="~/Views/Shared/_Notification.cshtml" />
    <!-- Main JS -->
    <script src="~/Dashboard/assets/js/main.js"></script>

    <!-- Place this tag in your head or just before your close body tag. -->
    <script src="~/js/site.js" asp-append-version="true"></script>
    <!-- Another JS -->
    <script async defer src="https://buttons.github.io/buttons.js"></script>
    @await RenderSectionAsync("Scripts", required: false)


</body>
</html>
