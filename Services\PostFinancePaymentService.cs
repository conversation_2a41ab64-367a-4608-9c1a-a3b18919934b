﻿using PostFinanceCheckout.Model;
using PostFinanceCheckout.Service;
using PostFinanceCheckout.Client;
using TaskDotNet.Models;
using TaskDotNet.Services.Interfaces;
using Comman.Helper.Extensions;

namespace TaskDotNet.Services
{
    public class PostFinancePaymentService : IPostFinancePaymentService
    {


        private readonly long spaceId;
        private readonly string applicationUserID;
        private readonly string authenticationKey;
        private readonly Configuration configuration;
        private readonly TransactionService transactionService;
        private readonly IConfiguration appconfiguration;

        public PostFinancePaymentService(IConfiguration Appconfiguration)
        {
            appconfiguration = Appconfiguration;
            this.spaceId = long.Parse(appconfiguration.GetSection("PostFinancePayment")["SpaceId"]);
            this.authenticationKey = appconfiguration.GetSection("PostFinancePayment")["AuthenticationKey"];
            this.applicationUserID = appconfiguration.GetSection("PostFinancePayment")["ApplicationUserID"];
            this.configuration = new Configuration(this.applicationUserID, this.authenticationKey);
            this.transactionService = new TransactionService(configuration);
        }


        public Transaction CreateTransaction(Partner user, decimal amount, string baseUrl)
        {
            return CreateTransaction(user, amount, baseUrl, "Rechare Balance",
                $"{baseUrl}/Partner/SuccessRechargeCredit",
                $"{baseUrl}/Partner/FailedRechargeCredit");
        }

        public Transaction CreateTransaction(Partner user, decimal amount, string baseUrl, string itemName, string successUrl, string failedUrl)
        {
            var currentCulture = Thread.CurrentThread.CurrentCulture.Name;

            AddressCreate billingAddress = new AddressCreate
            {
                Salutation = user.Salute.GetDisplayName(),                       
                GivenName = user.PName,             
                FamilyName = "",               
                Gender = user.Salute == Helper.Salute.Mr? Gender.MALE: Gender.FEMALE,     
                Country = user?.Land?[..2]??"CH",                           
                City = user.PCity,                         
                Postcode = user.PPostBox,                       
                DateOfBirth = new DateTime(1988, 4, 19), 
                OrganizationName = user.CompanyName,       
                MobilePhoneNumber = user.PhoneNumber,     
                EmailAddress = user.Email                 
                
            };

            var lineItem = new LineItemCreate(
                name: itemName,
                uniqueId: Guid.NewGuid().ToString(),
                type: LineItemType.PRODUCT,
                quantity: 1,
                amountIncludingTax: amount
            )
            {
                Sku = "order-total",
                ShippingRequired = false 
            };

            var transactionCreate = new TransactionCreate(new List<LineItemCreate> { lineItem })
            {
                BillingAddress = billingAddress,
                ShippingAddress = billingAddress,
                CustomerEmailAddress = billingAddress.EmailAddress,
                CustomerId = user.Id,
                MerchantReference = Guid.NewGuid().ToString(),
                InvoiceMerchantReference = "order-1",
                SuccessUrl = successUrl,
                FailedUrl = failedUrl,
                ShippingMethod = "Online Shipping",
                ChargeRetryEnabled = false,
                AllowedPaymentMethodConfigurations = new List<long?> {
                        284882L, // Twint
                        284881L, // Credit / Debit Card
                        285729L, // Cryptocurrency
                        284883L, // PostFinance Card
                        284884L  // PostFinance Pay
                    },
                Language = currentCulture,
                Currency = "CHF"
            };

            try
            {
                var transaction = transactionService.CreateWithHttpInfo(spaceId, transactionCreate);
                return transaction.Data;
            }
            catch (ApiException e)
            {
                throw new Exception($"Failed to create transaction. Reason: {e.Message}", e);
            }
        }


        public string GetPaymentPageUrl(long? transactionId)
        {
            var transactionPaymentPageService = new TransactionPaymentPageService(configuration);
            try
            {
                return transactionPaymentPageService.PaymentPageUrl(spaceId, transactionId);

            }
            catch (ApiException e)
            {
                throw new Exception($"Failed to get payment page URL. Reason: {e.Message}", e);
            }
        }
        public Transaction GetTransaction(long transactionId)
        {
            try
            {
                return transactionService.Read(spaceId, transactionId);
            }
            catch (ApiException e)
            {
                throw new Exception($"Failed to retrieve transaction. Reason: {e.Message}", e);
            }
        }
    }
}
