﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
#nullable disable

using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using TaskDotNet.Models;

namespace TaskDotNet.Areas.Identity.Pages.Account
{
    public class RegisterModel : PageModel
    {
        private readonly SignInManager<Partner> _signInManager;
        private readonly UserManager<Partner> _userManager;
        private readonly IUserStore<Partner> _userStore;
        private readonly IUserEmailStore<Partner> _emailStore;
        private readonly ILogger<RegisterModel> _logger;
        private readonly IMailService _emailSender;
        private readonly IEmailHtmlTemplateService emailHtmlTemplateService;

        public RegisterModel(
            UserManager<Partner> userManager,
            IUserStore<Partner> userStore,
            SignInManager<Partner> signInManager,
            ILogger<RegisterModel> logger,
            IMailService emailSender,
            IEmailHtmlTemplateService emailHtmlTemplateService)
        {
            _userManager = userManager;
            _userStore = userStore;
            _emailStore = GetEmailStore();
            _signInManager = signInManager;
            _logger = logger;
            _emailSender = emailSender;
            this.emailHtmlTemplateService = emailHtmlTemplateService;
        }

        [BindProperty]
        public InputModel Input { get; set; }
        public string ReturnUrl { get; set; }
        public IList<AuthenticationScheme> ExternalLogins { get; set; }
        public class InputModel
        {
            [Required]
            [EmailAddress]
            [Display(Name = "Email")]
            public string Email { get; set; }
            [Display(Name = "Company name")]
            public string CompanyName { get; set; }

            [Required]
            [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
            [DataType(DataType.Password)]
            [Display(Name = "Password")]
            public string Password { get; set; }

            [DataType(DataType.Password)]
            [Display(Name = "Confirm password")]
            [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
            public string ConfirmPassword { get; set; }
        }


        public async Task OnGetAsync(string returnUrl = null)
        {
            ReturnUrl = returnUrl;
            ExternalLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
        }

        public async Task<IActionResult> OnPostAsync(string returnUrl = null)
        {
            returnUrl ??= Url.Content("~/");
            ExternalLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
            if (!ModelState.IsValid)
            {
                return Page();
            }
            var user = CreateUser();

            await _userStore.SetUserNameAsync(user, Input.Email, CancellationToken.None);
            await _emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);
            user.CompanyName = Input.CompanyName;
            user.PName = Input.CompanyName;
            user.StartDate = DateTime.Now;

            var result = await _userManager.CreateAsync(user, Input.Password);

            if (!result.Succeeded)
            {
                foreach (var error in result.Errors)
                {
                    if (string.Equals( error.Code , "DuplicateUserName"))
                        continue;

                    ModelState.AddModelError("", error.Description);
                }
                return Page();
            }

            await _userManager.AddToRoleAsync(user, "Partner");
            return RedirectToPage("SendEmailConfirmation", new { email = Input.Email, returnUrl = returnUrl });

        }

        private Partner CreateUser()
        {
            try
            {
                return Activator.CreateInstance<Partner>();
            }
            catch
            {
                throw new InvalidOperationException($"Can't create an instance of '{nameof(Partner)}'. " +
                    $"Ensure that '{nameof(Partner)}' is not an abstract class and has a parameterless constructor, or alternatively " +
                    $"override the register page in /Areas/Identity/Pages/Account/Register.cshtml");
            }
        }

        private IUserEmailStore<Partner> GetEmailStore()
        {
            if (!_userManager.SupportsUserEmail)
            {
                throw new NotSupportedException("The default UI requires a user store with email support.");
            }
            return (IUserEmailStore<Partner>)_userStore;
        }
    }
}
