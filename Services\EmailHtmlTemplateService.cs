﻿using Admin.TaskDotNet.Dtos;
using Comman.Helper.Extensions;
using Comman.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using MimeKit;
using System.Drawing;
using TaskDotNet.DTOs;
using TaskDotNet.Helper.Extensions;
using TaskDotNet.Localization;

namespace TaskDotNet.Services
{
    public class EmailHtmlTemplateService : IEmailHtmlTemplateService
    {
        #region Ctor
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        private readonly string ActivitesLink;
        public EmailHtmlTemplateService(IConfiguration configuration, IWebHostEnvironment environment, IStringLocalizer<SharedResource> sharedLocalizer)
        {
            _configuration = configuration;
            _environment = environment;
            SharedLocalizer = sharedLocalizer;
            ActivitesLink = _configuration["ProjectsLinks:ActivitesLink"];
        }
        #endregion

        #region Actvate Email Template
        public async Task<string> GetActvateEmailTemplate(string Email, string callbackUrl)
        {
            var EmailImages = _configuration.GetSection("EmailImages");

            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ActivateEmail.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = await SourceReader.ReadToEndAsync();

            }
            string messageBody = string.Format(builder.HtmlBody, Email, callbackUrl);
            return messageBody;

        }
        #endregion

        #region ThankYou Email Template
        public string GetThankYouTemplate(string salute, string name, string lang)
        {

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ThankYou-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string messageBody = string.Format(builder.HtmlBody, salute, name);
            return messageBody;

        }
        #endregion 
        
        #region Customer ThankYou Email Template
        public string GetCustomerThankYouTemplate(string salute, string name, string lang)
        {

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ThankYouCustomer-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string messageBody = string.Format(builder.HtmlBody, salute, name);
            return messageBody;

        }
        #endregion

        #region OTP Email Template
        public async Task<string> GetOTPTemplateAsync(string name, string otp, string lang)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\OTP-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = await SourceReader.ReadToEndAsync();

            }
            string messageBody = string.Format(builder.HtmlBody, otp);
            return messageBody;

        }
        #endregion

        #region  Reset Password Template
        public string GetResetPasswordemplate(string name, string lang, string callbacklink)
        {

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\ResetPassword-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string messageBody = string.Format(builder.HtmlBody, name, callbacklink);
            return messageBody;

        }
        #endregion

        #region Admin Email For New Partner Template
        public string GetAdminEmailForNewPartner(string Email, string CompanyName, DateTime StartDate)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\AdminMessgAfterRegister.html";

            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {
                builder.HtmlBody = SourceReader.ReadToEnd();
            }

            string messageBody = builder.HtmlBody
                .Replace("{{0}}", headerImage)
                .Replace("{{CompanyName}}", CompanyName)
                .Replace("{{Email}}", Email)
                .Replace("{{StartDate}}", StartDate.ToString("dd.MM.yyyy"));

            return messageBody;
        }
        #endregion


        #region Request Frontend Moving Email Template
        public string GetRequestFrontendMovingTemplate(string userCompanyName, MovingDto dto, string lang,string inventoryHtml)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\RequestFrontendMoving-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string viewingDateString = dto.ViewingDate != DateTime.MinValue ? SharedResource.YesOn + " " + dto.ViewingDate.ToString("dd.MM.yyyy") : SharedResource.NotRequired;

            var notes = dto.Notes != null ? $"<p style=\" color: darkblue\"><span style=\"font-weight:bold\">Notes: </span>{dto.Notes}</p>" : "";
            var fdistance = dto.FDistance > 0 ? $"{dto.FDistance} m {SharedResource.toTheLoadingEdge}" : "";
            var tdistance = dto.TDistance > 0 ? $"{dto.TDistance} m {SharedResource.toTheDrainEdge}" : "";
            var kartons = dto.Kartons > 0 ? $"{dto.Kartons} {SharedResource.Boxes} (30kg)" : "";

            string messageBody = string.Format(builder.HtmlBody,
                dto.OrderNr, //0
                userCompanyName, //1
                dto.MovingDate.ToString("dd.MM.yyyy"), //2
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //3
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //4
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //5
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //6
                dto.PostBox + " " + dto.City, //7
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //8
                dto.TPostBox + " " + dto.TCity, //9
                dto.Room, //10
                dto.Object, //11
                dto.Area, //12
                dto.Floor, //13
                dto.FLift ? SharedResource.Yes : SharedResource.No, //14
                fdistance, //15
                dto.TObject, //16
                dto.TFloor, //17
                dto.TLift ? SharedResource.Yes : SharedResource.No, //18
                tdistance, //19
                dto.FDismantFurnit ? SharedResource.Yes : SharedResource.No, //20
                dto.FDismantLamp ? SharedResource.Yes : SharedResource.No, //21
                dto.FWrapUp ? SharedResource.Yes : SharedResource.No, //22
                dto.Mobellift ? SharedResource.Yes : SharedResource.No, //23
                dto.Klavier ? SharedResource.Yes : SharedResource.No, //24
                dto.Schwer ? SharedResource.Yes : SharedResource.No, //25
                dto.Celler ? SharedResource.Yes : SharedResource.No, //26
                dto.Garage ? SharedResource.Yes : SharedResource.No, //27
                dto.TMountMöbel ? SharedResource.Yes : SharedResource.No, //28
                dto.TmontLamp ? SharedResource.Yes : SharedResource.No, //29
                dto.TAuspacken ? SharedResource.Yes : SharedResource.No, //30
                dto.Lager ? SharedResource.Yes : SharedResource.No, //31
                dto.Disposal ? SharedResource.Yes : SharedResource.No, //32
                kartons, //33
                dto.Focus, //34
                viewingDateString, //35
                inventoryHtml, //36
                ActivitesLink, //37
                headerImage, //38
                notes //39
                );
            return messageBody;

        }
        #endregion

        #region Request Frontend Moving and Cleaning Email Template
        public string GetRequestFrontendMovingAndCleaningTemplate(string userCompanyName, MovingCleaningDto dto, string lang, string inventoryHtml)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\RequestFrontendMovingAndCleaning-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string viewingDateString = dto.ViewingDate != DateTime.MinValue ? SharedResource.YesOn + " " + dto.ViewingDate.ToString("dd.MM.yyyy") : SharedResource.NotRequired;
            var notes = dto.Notes != null ? $"<p style=\" color: darkblue\"><span style=\"font-weight:bold\">Notes: </span>{dto.Notes}</p>" : "";
            var fdistance = dto.FDistance > 0 ? $"{dto.FDistance} m {SharedResource.toTheLoadingEdge}" : "";
            var tdistance = dto.TDistance > 0 ? $"{dto.TDistance} m {SharedResource.toTheDrainEdge}" : "";
            var kartons = dto.Kartons > 0 ? $"{dto.Kartons} {SharedResource.Boxes} (30kg)" : "";

            string messageBody = string.Format(builder.HtmlBody,
                dto.OrderNr, //0
                userCompanyName, //1
                dto.MovingDate.ToString("dd.MM.yyyy"), //2
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //3
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //4
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //5
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //6
                dto.PostBox + " " + dto.City, //7
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //8
                dto.TPostBox + " " + dto.TCity, //9
                dto.Room, //10
                dto.Object, //11
                dto.Area, //12
                dto.Floor, //13
                dto.FLift ? SharedResource.Yes : SharedResource.No, //14
                fdistance, //15
                dto.TObject, //16
                dto.TFloor, //17
                dto.TLift ? SharedResource.Yes : SharedResource.No, //18
                tdistance, //19
                dto.FDismantFurnit ? SharedResource.Yes : SharedResource.No, //20
                dto.FDismantLamp ? SharedResource.Yes : SharedResource.No, //21
                dto.FWrapUp ? SharedResource.Yes : SharedResource.No, //22
                dto.Mobellift ? SharedResource.Yes : SharedResource.No, //23
                dto.Klavier ? SharedResource.Yes : SharedResource.No, //24
                dto.Schwer ? SharedResource.Yes : SharedResource.No, //25
                dto.Celler ? SharedResource.Yes : SharedResource.No, //26
                dto.Garage ? SharedResource.Yes : SharedResource.No, //27
                dto.TMountMöbel ? SharedResource.Yes : SharedResource.No, //28
                dto.TmontLamp ? SharedResource.Yes : SharedResource.No, //29
                dto.TAuspacken ? SharedResource.Yes : SharedResource.No, //30
                dto.Lager ? SharedResource.Yes : SharedResource.No, //31
                dto.Disposal ? SharedResource.Yes : SharedResource.No, //32
                kartons, //33
                dto.Focus, //34
                viewingDateString, //35
                dto.Room, //36
                dto.Object, //37
                dto.Floor, //38
                dto.CleaningDate.ToString("dd.MM.yyyy"), //39
                dto.HandOverDate.ToString("dd.MM.yyyy"), //40
                inventoryHtml, //41
                ActivitesLink, //42
                headerImage, //43
                notes //44
                );
            return messageBody;
        }
        #endregion

        #region Request Frontend Workers Email Template
        public string GetRequestFrontendWorkersTemplate(string userCompanyName, WorkersDto dto, string lang)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\RequestFrontendWorkers-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }

            var notes = dto.Notes != null ? $"<p style=\" color: darkblue\"><span style=\"font-weight:bold\">Notes: </span>{dto.Notes}</p>" : "";

            string messageBody = string.Format(builder.HtmlBody,
                dto.OrderNr, //0
                userCompanyName, //1
                dto.ActivityType.GetDisplayName(), //2
                dto.MovingDate.ToString("dd.MM.yyyy"), //3
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //4
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //5
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //6
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //7
                dto.PostBox + " " + dto.City, //8
                dto.Object, //9
                dto.Flexible, //10
                dto.Focus, //11
                ActivitesLink, //12
                headerImage, //13
                notes //14

                );
            return messageBody;

        }
        #endregion

        #region Request Frontend Paintng Email Template
        public string GetRequestFrontendPaintingTemplate(string userCompanyName, PaintingDto dto, string lang)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];

            var pathToFile = $"{_environment.WebRootPath}\\Templates\\RequestFrontendPainting-{lang}.html";
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            string viewingDateString = dto.ViewingDate != DateTime.MinValue ? SharedResource.YesOn + " " + dto.ViewingDate.ToString("dd.MM.yyyy") : SharedResource.NotRequired;
            var notes = dto.Notes != null ? $"<p style=\" color: darkblue\"><span style=\"font-weight:bold\">Notes: </span>{dto.Notes}</p>" : "";


            string messageBody = string.Format(builder.HtmlBody,
                dto.OrderNr, //0
                userCompanyName, //1
                dto.MovingDate.ToString("dd.MM.yyyy"), //2
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //3
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //4
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //5
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //6
                dto.PostBox + " " + dto.City, //7
                dto.Room, //8
                dto.Object, //9
                dto.Area, //10
                dto.Floor, //11
                dto.Washroom ? SharedResource.Yes : SharedResource.No, //12
                dto.Flexible, //13
                dto.Focus, //14
                viewingDateString, //15
                dto.Walls, //16
                dto.Doors, //17
                dto.Windows, //18
                ActivitesLink, //19
                headerImage,  //20
                notes //21
                );
            return messageBody;

        }
        #endregion

        #region Request Frontend Cleaning Email Template
        public string GetRequestFrontendCleaningTemplate(string userCompanyName, CleaningDto dto, string lang)
        {
            var EmailImages = _configuration.GetSection("EmailImages");
            var headerImage = EmailImages["companyLogo"];


            var pathToFile = $"{_environment.WebRootPath}\\Templates\\RequestFrontendCleaning-{lang}.html";

            // Determine the ViewingDate boolean value
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText(pathToFile))
            {

                builder.HtmlBody = SourceReader.ReadToEnd();

            }
            var notes = dto.Notes != null ? $"<p style=\" color: darkblue\"><span style=\"font-weight:bold\">Notes: </span>{dto.Notes}</p>" : "";

            string messageBody = string.Format(builder.HtmlBody,
                dto.OrderNr, //0
                userCompanyName, //1
                headerImage, //2
                dto.CleaningDate.ToString("dd.MM.yyyy"), //3
                dto.HandOverDate.ToString("dd.MM.yyyy"), //4
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //5
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //6
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //7
                "xxxxxxxxxxxxxxxxxxxxxxxxxxx", //8
                dto.PostBox + " " + dto.City, //9
                dto.Room, //10
                dto.Object, //11
                dto.Area, //12
                dto.Floor, //13
                dto.CleaningType, //14
                dto.SoilType, //15
                dto.CarpetCleaning ? SharedResource.Yes : SharedResource.No, //16
                dto.HighPressure ? SharedResource.Yes : SharedResource.No, //17
                dto.Balcony ? SharedResource.Yes : SharedResource.No, //18
                dto.Shutters ? SharedResource.Yes : SharedResource.No, //19
                dto.Washroom ? SharedResource.Yes : SharedResource.No, //20
                dto.Flexible, //21
                dto.VenetianBlinds ? SharedResource.Yes : SharedResource.No, //23
                dto.Focus, //22
                dto.BathroomToilet, //24
                dto.ShowerToilet, //25
                dto.Heater, //26
                dto.Doors, //27
                dto.Windows, //28
                ActivitesLink, //29
                dto.Toilets, //30
                notes //31
                );
            return messageBody;

        }
        #endregion

    }

}
