﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
#nullable disable

using Comman.Services.Interfaces;
using Humanizer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNet.Services;

namespace TaskDotNet.Areas.Identity.Pages.Account
{
    [AllowAnonymous]
    public class RegisterConfirmationModel : PageModel
    {
        private readonly UserManager<Partner> _userManager;
        private readonly IMailService _sender;
        private readonly IOTPVerificationService _OTPVerificationService;
        private readonly IUserEmailStore<Partner> _emailStore;
        private readonly IUserStore<Partner> _userStore;
        private readonly IEmailHtmlTemplateService _emailHtml;
        private readonly ApplicationDbContext _context;

        public RegisterConfirmationModel(UserManager<Partner> userManager, IMailService sender, IOTPVerificationService oTPVerificationService, IUserStore<Partner> userStore, IEmailHtmlTemplateService emailHtml, ApplicationDbContext context)
        {
            _userManager = userManager;
            _sender = sender;
            _OTPVerificationService = oTPVerificationService;
            _userStore = userStore;

            _emailStore = GetEmailStore();
            _emailHtml = emailHtml;
            _context = context;
        }

        [BindProperty]
        public string Email { get; set; }
        [BindProperty]
        public string Code { get; set; }
        public string StatusMessage { get; private set; }

        public async Task<IActionResult> OnGetAsync(string email)
        {
            if (email == null)
            {
                return RedirectToPage("/Index");
            }
            var user = await _userManager.FindByEmailAsync(email);

            if (user == null)
            {
                return NotFound($"Unable to load user with email '{email}'.");
            }

            Email = email;

            return Page();
        }
        public async Task<IActionResult> OnPostAsync()
        {
            if (Email == null || Code == null)
            {
                return RedirectToPage("/Account/login");
            }
            var user = await _userManager.FindByEmailAsync(Email);

            if (user == null)
            {
                return NotFound($"Unable to load user with email '{Email}'.");
            }

            var result = await ConfirmEmailAsync(user, Code);

            if (!result)
            {
                ModelState.AddModelError("Code", "Code Not Correct, Please try again!");
                Code = "";
                return Page();
            }

            string body = _emailHtml.GetAdminEmailForNewPartner(user.Email, user.CompanyName, user.StartDate ?? DateTime.Today);

            var companyEmail = await _context.Company.Select(x => x.Email).FirstOrDefaultAsync(x => true);
            var mailRequest = new MailRequest
            {
                ToEmail = companyEmail,
                Subject = "New Partner Registration",
                Body = body
            };
            await _sender.SendEmailAsync(mailRequest, default);

            return RedirectToPage("/Account/ConfirmEmail");
        }

        private async Task<bool> ConfirmEmailAsync(Partner user, string code)
        {
            var otp = await _OTPVerificationService.GetByEmailAsync(user.Email);

            if (otp == null || otp.OTP != Code)
            {
                return false;
            }
            await _emailStore.SetEmailConfirmedAsync(user, true, CancellationToken.None);
            await _userManager.UpdateAsync(user);

            await _OTPVerificationService.DeleteAsync(otp);
            return true;
        }

        private IUserEmailStore<Partner> GetEmailStore()
        {
            if (!_userManager.SupportsUserEmail)
            {
                throw new NotSupportedException("The default UI requires a user store with email support.");
            }
            return (IUserEmailStore<Partner>)_userStore;
        }
    }
}
