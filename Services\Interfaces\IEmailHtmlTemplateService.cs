﻿using Admin.TaskDotNet.Dtos;
using TaskDotNet.DTOs;
using TaskDotNet.Models;

namespace Comman.Services.Interfaces
{
    public interface IEmailHtmlTemplateService
    {
        Task<string> GetActvateEmailTemplate(string Email, string callbackUrl);
        public string GetThankYouTemplate(string salute, string name, string lang);
        public string GetCustomerThankYouTemplate(string salute, string name, string lang);
        Task<string> GetOTPTemplateAsync(string name, string otp, string lang);
        public string GetResetPasswordemplate(string name,string lang, string callbacklink);
        string GetAdminEmailForNewPartner(string Email, string CompanyName, DateTime StartDate);

        public string GetRequestFrontendMovingTemplate(string userCompanyName, MovingDto dto, string lang, string inventoryHtml);
        public string GetRequestFrontendMovingAndCleaningTemplate(string userCompanyName, MovingCleaningDto dto, string lang, string inventoryHtml);
        public string GetRequestFrontendCleaningTemplate(string userCompanyName, CleaningDto dto, string lang);
        public string GetRequestFrontendWorkersTemplate(string userCompanyName, WorkersDto dto, string lang);
        public string GetRequestFrontendPaintingTemplate(string userCompanyName, PaintingDto dto, string lang);

    }
}
