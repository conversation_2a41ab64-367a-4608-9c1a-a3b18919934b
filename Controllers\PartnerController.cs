﻿ using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.DTos;
using TaskDotNet.Helper;
using TaskDotNet.Helper.Filters;
using TaskDotNet.Models;
using Microsoft.AspNetCore.Mvc.Rendering;
using TaskDotNet.Helper.Extensions;
using Comman.Helper.Extensions;
using TaskDotNet.Services.Interfaces;
using PostFinanceCheckout.Model;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;

namespace TaskDotNet.Controllers
{
    [Authorize(Roles = "Partner")]
    public class PartnerController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<Partner> _userManager;
        private readonly IMapper _mapper;
        private readonly IPostFinancePaymentService _postFinancePaymentService;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        public PartnerController(ApplicationDbContext context, UserManager<Partner> userManager, IMapper mapper, IPostFinancePaymentService postFinancePaymentService, IStringLocalizer<SharedResource> sharedLocalizer)
        {
            _context = context;
            _userManager = userManager;
            _mapper = mapper;
            _postFinancePaymentService = postFinancePaymentService;
            SharedLocalizer = sharedLocalizer;
        }

        // GET: Partner/Index
        public async Task<IActionResult> Index()
        {
            // Get the current authenticated partner with related entities
            var partner = await _context.Partners
                .AsNoTracking()
                .Include(p => p.PartnerCountries)
                .Include(p => p.PartnerActivities)
                .FirstOrDefaultAsync(p => p.Id == _userManager.GetUserId(User)); 

            if (partner == null)
            {
                return NotFound(); // Return NotFound if the partner does not exist
            }

            partner.PartnerCountries ??= new PartnerCountries();
            partner.PartnerActivities ??= new PartnerActivities();

            ViewBag.PartnerCountries = SplitKantons(partner.PartnerCountries);
            ViewBag.PartnerActivities = PartnerHelper.GetBooleanProperties(partner.PartnerActivities);


            // **Mapping the partner entity to the PartnerDto**
            var model = _mapper.Map<PartnerDto>(partner);  // AutoMapper converts Partner to PartnerDto
            model.PEmail = partner.Email;
            model.PSaldo = partner.Saldo;

            return View(model);
        }

        // POST: Partner/Index
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Index(PartnerDto partnerDto)
        {
            if (!ModelState.IsValid)
            {
                ViewBag.PartnerCountries = SplitKantons(partnerDto.PartnerCountries);
                ViewBag.PartnerActivities = PartnerHelper.GetBooleanProperties(partnerDto.PartnerActivities);
                return View(partnerDto);
            }

            // Get the current authenticated partner with related entities
            var partner = await _context.Partners
                .AsNoTracking()
                .Include(p => p.PartnerCountries)
                .Include(p => p.PartnerActivities)
                .FirstOrDefaultAsync(p => p.Id == _userManager.GetUserId(User));

            if (partner == null)
            {
                return NotFound();
            }

            if(partnerDto.UID != partner.UID)
            {
                SetBlockedStatusIfUIDPreviouslyBlocked(partner);
            }

            // Map the PartnerDto to the Partner entity
            _mapper.Map(partnerDto, partner);
            partner.IsCompletedData = true;

            // Store the IsCompletedData value in session
            HttpContext.Session.SetString("IsCompletedData", partner.IsCompletedData.ToString());

            _context.Partners.Update(partner);
            // Directly save changes
            await _context.SaveChangesAsync();

            TempData["success"] = SharedLocalizer["PartnerDataUpdatedSuccessfully"].Value;
            return RedirectToAction("Index", "Home");
        }


        [ServiceFilter(typeof(ValidatePartnerAccountStatusFilter))]
        public async Task<IActionResult> Statistics()
        {
            var allMovements = _context.Movements
                .Where(m => m.PartnerId == _userManager.GetUserId(User) && m.Purchase_date.Year == DateTime.UtcNow.Year)
                .Include(m => m.Activity)
                .AsNoTracking()
                .ToList();

            var partnerActivity = await _context.PartnerActivities.FirstOrDefaultAsync(p => p.PartnerId == _userManager.GetUserId(User));

            List<ActivityType> partnerActivities = GetPartnerActivities(partnerActivity);

            var currentDate = DateTime.UtcNow;
            var dataCountsByCategoryAndYearMonth = new Dictionary<string, Dictionary<string, int>>();
            foreach (ActivityType activityType in partnerActivities)
            {
                var countsByYearMonth = new Dictionary<string, int>();

                for (int month = 1; month <= 12; month++)
                {
                    var startDate = new DateTime(currentDate.Year, month, 1);
                    var endDate = startDate.AddMonths(1).AddTicks(-1);

                    var count = allMovements.Where(
                            e => e.Activity.ActivityType == activityType
                            && e.Purchase_date >= startDate && e.Purchase_date <= endDate
                        ).Count();

                    countsByYearMonth.Add(startDate.ToString("MMMM"), count);
                }
                dataCountsByCategoryAndYearMonth.Add(activityType.GetDisplayName(), countsByYearMonth);
            }


            var eachActivityCountAndSum = new Dictionary<string, (int Count, decimal Sum)>();
            foreach (ActivityType activityType in partnerActivities)
            {
                var count = allMovements.Where(e => e.Activity.ActivityType == activityType).Count();
                var sum = allMovements.Where(e => e.Activity.ActivityType == activityType).Sum(e => e.Order_Price);

                eachActivityCountAndSum.Add(activityType.GetDisplayName(), (count, sum));
            }
            ViewBag.EachActivityCountAndSum = eachActivityCountAndSum;

            return View(dataCountsByCategoryAndYearMonth);
        }


        [ServiceFilter(typeof(ValidatePartnerAccountStatusFilter))]
        public async Task<IActionResult> RechargeCredit(decimal? amount)
        {
            var partner = await _context.Partners.FirstOrDefaultAsync(p => p.Id == _userManager.GetUserId(User));

            if (partner == null)
            {
                return NotFound();
            }

            var model = new RechargeCreditDto
            {
                Email = partner.Email,
                City = partner.PCity,
                PostBox = partner.PPostBox,
                CurrentBalance = partner.Saldo,
            };

            ViewBag.Amount = amount ?? 0.00m;
            return View(model);
        }
        public IActionResult TermsAndConditions()
        {
            return View();
        }

        public IActionResult LegalNotice()
        {
            return View();
        }

        public IActionResult PrivacyPolicy()
        {
            return View();
        }
        public IActionResult Disclaimer()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> RechargeCredit(decimal Amount)
        {
            var partner = await _userManager.GetUserAsync(User);
            var baseUrl = $"{Request.Scheme}://{Request.Host}";

            var transaction = _postFinancePaymentService.CreateTransaction(partner, Amount, baseUrl);

            if (transaction != null)
            {

                HttpContext.Session.SetString("RechargeTransactionId", transaction.Id.ToString());

                var paymentPageUrl = _postFinancePaymentService.GetPaymentPageUrl(transaction.Id);
                if (paymentPageUrl != null)
                {
                    return Redirect(paymentPageUrl);
                }
            }

            TempData["error"] = SharedLocalizer["SomeThingWentWrong"].Value;
            return View();
        }


        [HttpGet]
        public async Task<IActionResult> SuccessRechargeCredit()
        {
            var transactionIdStr = HttpContext.Session.GetString("RechargeTransactionId");

            if (!long.TryParse(transactionIdStr, out var transactionId))
            {
                TempData["error"] = SharedLocalizer["RechargeFailedMessage"].Value;
                return RedirectToAction(nameof(RechargeCredit));
            }

            var transaction = _postFinancePaymentService.GetTransaction(transactionId);

            if (transaction.State != TransactionState.AUTHORIZED &&
                transaction.State != TransactionState.FULFILL &&
                transaction.State != TransactionState.COMPLETED)
            {
                TempData["error"] = SharedLocalizer["RechargeFailedMessage"].Value;
                return RedirectToAction(nameof(RechargeCredit));
            }

            var partner = await _userManager.GetUserAsync(User);
            partner.Saldo += transaction.LineItems.Sum(i => i.AmountIncludingTax) ?? 0;
            _context.Partners.Update(partner);

            string paymentMethodName = transaction.PaymentConnectorConfiguration?.Name;
            var balanceMovement = new BalanceMovement
            {
                PartnerId = partner.Id,
                Amount = transaction.LineItems.Sum(i => i.AmountIncludingTax) ?? 0,
                PaymentMethod = paymentMethodName,
                TransactionId = transactionId.ToString(),
                CreatedAt = DateTime.UtcNow
            };
            _context.BalanceMovements.Add(balanceMovement);

            await _context.SaveChangesAsync();

            HttpContext.Session.Remove("RechargeTransactionId");

            TempData["success"] = SharedLocalizer["RechargeSuccessfullyMessage"].Value;
            return RedirectToAction(nameof(RechargeCredit));
        }


        [HttpGet]
        public async Task<IActionResult> FailedRechargeCredit()
        {

            TempData["error"] = SharedLocalizer["RechargeFailedMessage"].Value;
            return RedirectToAction(nameof(RechargeCredit));
        }


        [ServiceFilter(typeof(ValidatePartnerAccountStatusFilter))]
        public async Task<IActionResult> Activities(PaginatedRequest request)
        {
            var partnerId = _userManager.GetUserId(User);

            // Get the partner with their activities and countries
            var partner = await _context.Partners
                .Include(p => p.PartnerActivities)
                .Include(p => p.PartnerCountries)
                .FirstOrDefaultAsync(p => p.Id == partnerId);

            if (partner == null)
            {
                return NotFound(); // Return empty if no partner found
            }

            // Build the query for activities
            var query = _context.Activities.AsQueryable();

            // Get all kantons where the partner works dynamically
            var partnerCountries = partner.PartnerCountries.GetType().GetProperties()
                .Where(p => p.PropertyType == typeof(bool) && (bool)p.GetValue(partner.PartnerCountries))
                .Select(p => p.Name)
                .ToList(); // Convert to list to avoid multiple enumeration

            // Filter activities by matching kantons
            query = query.Where(a => partnerCountries.Contains(a.Kanton));
            List<ActivityType> partnerActivities = GetPartnerActivities(partner.PartnerActivities);

            // Filter activities by matching activity type
            query = query.Where(a => partnerActivities.Contains(a.ActivityType));


            if (request.SearchTerm != 0)
            {
                query = query.Where(m => m.ActivityType == (ActivityType)request.SearchTerm);
            }


            query = query.AsNoTracking().Where(a =>
                (a.ActivityType == ActivityType.Cleaning && a.CleaningDate >= DateTime.Today) ||
                (a.ActivityType == ActivityType.Moving && a.MovingDate >= DateTime.Today));

            int totalCount = query.Count();

            IEnumerable<Activity> activities = query.OrderByDescending(a => a.ActivityType == ActivityType.Cleaning ? a.CleaningDate : a.MovingDate)
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize);

            ViewBag.PartnerActivities = partnerActivities.Select(m => new SelectListItem { Value = ((int)m).ToString(), Text = m.GetDisplayName() });

            ViewBag.Salodo = partner.Saldo;
            return View(new PaginatedListResponse<Activity>(
                data: activities ?? Enumerable.Empty<Activity>(),
                totalCount: totalCount,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                searchTerm: request.SearchTerm
            ));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Checkout([FromBody] CheckoutRequestModel model)
        {
            if (model == null || model.ActivityId == 0)
            {
                return Json(new { success = false, message = SharedLocalizer["InvalidInput"].Value });
            }


            try
            {
                // Fetch activity and partner
                var activity = await _context.Activities
                    .Include(a => a.InventoryItems)
                    .FirstOrDefaultAsync(a => a.Id == model.ActivityId);

                if (activity == null)
                {
                    return Json(new { success = false, message = SharedLocalizer["NotFoundMessage"].Value });
                }

                var moveMent = _context.Movements
                    .Where(m => m.PartnerId == _userManager.GetUserId(User) && m.Order_Nr == activity.OrderNr)
                    .Any();

                if (moveMent)
                {
                    return Json(new { success = false, message = SharedLocalizer["AlreadyPurchased"].Value });
                }

                var partner = await _context.Partners
                    .FirstOrDefaultAsync(p => p.Id == _userManager.GetUserId(User));

                if (partner == null)
                {
                    return Json(new { success = false, message = SharedLocalizer["NotFoundMessage"].Value });
                }

                // Check balance
                if (partner.Saldo < activity.Preis)
                {
                    return Json(new { success = false, message = SharedLocalizer["NotEnoughBalance"].Value, amount = activity.Preis });
                }

                // Deduct balance
                partner.Saldo -= activity.Preis;

                // Map Activity to MovementActivity
                var movementActivity = _mapper.Map<MovementActivity>(activity);

                // Create Movement
                var movement = new Movement
                {
                    Partner_Name = partner.PName,
                    Purchase_date = DateTime.Today,
                    Order_Nr = activity.OrderNr,
                    Order_Price = activity.Preis,
                    PaymentWay = "Balance",
                    ActivityType = activity.ActivityType,
                    PartnerId = partner.Id,
                    Activity = movementActivity,
                };

                // Update activity details
                activity.SalesCount++;
                activity.PaymentCountCompleted = activity.SalesCount == activity.PaymentCount;

                PayOrder payOrder = new PayOrder
                {
                    ActivityType = activity.ActivityType,
                    Amount = activity.Preis,
                    PayDate = DateTime.UtcNow,
                    PartnerId = partner.Id,
                    OrderNr = activity.OrderNr,
                };

                // Save changes in a transaction
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    _context.PayOrders.Add(payOrder);
                    _context.Movements.Add(movement);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }

                // Return success response
                return Json(new
                {
                    success = true,
                    message = SharedLocalizer["PaymentSuccesfully"].Value,
                    redirectUrl = Url.Action("ActivityDetails", "PurchasedActivites", new { id = movement.MovementActivityId })
                });
            }
            catch (Exception ex)
            {
                // Return a generic error message
                return Json(new { success = false, message = SharedLocalizer["SomeThingWentWrong"].Value });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateActivityPayment([FromBody] CheckoutRequestModel model)
        {
            if (model == null || model.ActivityId == 0)
            {
                return Json(new { success = false, message = SharedLocalizer["InvalidInput"].Value });
            }

            try
            {
                // Fetch activity and partner
                var activity = await _context.Activities
                    .FirstOrDefaultAsync(a => a.Id == model.ActivityId);

                if (activity == null)
                {
                    return Json(new { success = false, message = SharedLocalizer["NotFoundMessage"].Value });
                }

                // Check if already purchased
                var moveMent = _context.Movements
                    .Where(m => m.PartnerId == _userManager.GetUserId(User) && m.Order_Nr == activity.OrderNr)
                    .Any();

                if (moveMent)
                {
                    return Json(new { success = false, message = SharedLocalizer["AlreadyPurchased"].Value });
                }

                var partner = await _context.Partners
                    .FirstOrDefaultAsync(p => p.Id == _userManager.GetUserId(User));

                if (partner == null)
                {
                    return Json(new { success = false, message = SharedLocalizer["NotFoundMessage"].Value });
                }

                // Create payment transaction
                var baseUrl = $"{Request.Scheme}://{Request.Host}";
                var transaction = _postFinancePaymentService.CreateTransaction(
                    partner,
                    activity.Preis,
                    baseUrl,
                    $"Activity Purchase - {activity.ActivityType}",
                    $"{baseUrl}/Partner/SuccessActivityPayment",
                    $"{baseUrl}/Partner/FailedActivityPayment");

                if (transaction != null)
                {
                    // Store transaction and activity info in session
                    HttpContext.Session.SetString("ActivityPaymentTransactionId", transaction.Id.ToString());
                    HttpContext.Session.SetString("ActivityPaymentActivityId", activity.Id.ToString());

                    var paymentPageUrl = _postFinancePaymentService.GetPaymentPageUrl(transaction.Id);
                    if (paymentPageUrl != null)
                    {
                        return Json(new { success = true, paymentUrl = paymentPageUrl });
                    }
                }

                return Json(new { success = false, message = SharedLocalizer["SomeThingWentWrong"].Value });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = SharedLocalizer["SomeThingWentWrong"].Value });
            }
        }

        [HttpGet]
        public async Task<IActionResult> SuccessActivityPayment()
        {
            var transactionIdStr = HttpContext.Session.GetString("ActivityPaymentTransactionId");
            var activityIdStr = HttpContext.Session.GetString("ActivityPaymentActivityId");

            if (!long.TryParse(transactionIdStr, out var transactionId) || !int.TryParse(activityIdStr, out var activityId))
            {
                TempData["error"] = SharedLocalizer["PaymentFehler"].Value;
                return RedirectToAction(nameof(Activities));
            }

            var transaction = _postFinancePaymentService.GetTransaction(transactionId);

            if (transaction.State != TransactionState.AUTHORIZED &&
                transaction.State != TransactionState.FULFILL &&
                transaction.State != TransactionState.COMPLETED)
            {
                TempData["error"] = SharedLocalizer["PaymentFehler"].Value;
                return RedirectToAction(nameof(Activities));
            }

            try
            {
                // Fetch activity and partner
                var activity = await _context.Activities
                    .Include(a => a.InventoryItems)
                    .FirstOrDefaultAsync(a => a.Id == activityId);

                var partner = await _context.Partners
                    .FirstOrDefaultAsync(p => p.Id == _userManager.GetUserId(User));

                if (activity == null || partner == null)
                {
                    TempData["error"] = SharedLocalizer["PaymentFehler"].Value;
                    return RedirectToAction(nameof(Activities));
                }

                // Check if already purchased
                var existingMovement = _context.Movements
                    .Where(m => m.PartnerId == partner.Id && m.Order_Nr == activity.OrderNr)
                    .Any();

                if (existingMovement)
                {
                    TempData["error"] = SharedLocalizer["AlreadyPurchased"].Value;
                    return RedirectToAction(nameof(Activities));
                }

                // Map Activity to MovementActivity
                var movementActivity = _mapper.Map<MovementActivity>(activity);

                // Create Movement
                var movement = new Movement
                {
                    Partner_Name = partner.PName,
                    Purchase_date = DateTime.Today,
                    Order_Nr = activity.OrderNr,
                    Order_Price = activity.Preis,
                    PaymentWay = "Payment Gateway",
                    ActivityType = activity.ActivityType,
                    PartnerId = partner.Id,
                    Activity = movementActivity,
                };

                // Update activity details
                activity.SalesCount++;
                activity.PaymentCountCompleted = activity.SalesCount == activity.PaymentCount;

                PayOrder payOrder = new PayOrder
                {
                    ActivityType = activity.ActivityType,
                    Amount = activity.Preis,
                    PayDate = DateTime.UtcNow,
                    PartnerId = partner.Id,
                    OrderNr = activity.OrderNr,
                };

                // Save changes in a transaction
                using var dbTransaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    _context.PayOrders.Add(payOrder);
                    _context.Movements.Add(movement);
                    await _context.SaveChangesAsync();
                    await dbTransaction.CommitAsync();
                }
                catch
                {
                    await dbTransaction.RollbackAsync();
                    throw;
                }

                // Clear session
                HttpContext.Session.Remove("ActivityPaymentTransactionId");
                HttpContext.Session.Remove("ActivityPaymentActivityId");

                TempData["success"] = SharedLocalizer["PaymentSuccesfully"].Value;
                return RedirectToAction("ActivityDetails", "PurchasedActivites", new { id = movement.MovementActivityId });
            }
            catch (Exception ex)
            {
                TempData["error"] = SharedLocalizer["PaymentFehler"].Value;
                return RedirectToAction(nameof(Activities));
            }
        }

        [HttpGet]
        public async Task<IActionResult> FailedActivityPayment()
        {
            // Clear session
            HttpContext.Session.Remove("ActivityPaymentTransactionId");
            HttpContext.Session.Remove("ActivityPaymentActivityId");

            TempData["error"] = SharedLocalizer["PaymentFehler"].Value;
            return RedirectToAction(nameof(Activities));
        }

        #region Helper

        private void SetBlockedStatusIfUIDPreviouslyBlocked(Partner user)
        {
            var userWithSameUID = _userManager.Users.AsNoTracking().FirstOrDefault(u => u.UID == user.UID);
            if (userWithSameUID != null && userWithSameUID.Status == PartnerStatus.Blocked)
            {
                user.Status = PartnerStatus.Blocked;
                user.Evaluation = PartnerEvaluation.Useless;
            }
        }

        private static List<ActivityType> GetPartnerActivities(PartnerActivities partnerActivities)
        {


            return partnerActivities.GetType().GetProperties()
                .Where(p => p.PropertyType == typeof(bool) && (bool)p.GetValue(partnerActivities))
                .Select(p => Enum.Parse(typeof(ActivityType), p.Name))
                .Cast<ActivityType>()
                .ToList();

        }

        private List<CountryGroup> SplitKantons(PartnerCountries partnerCountries)
        {
            var countryGroups = new List<CountryGroup>
            {
                new CountryGroup
                {
                    LandCode = "CH",
                    LandName = "Switzerland",
                    Countries = new List<(string DisplayName,string Name, bool Value)>
                    {
                        ("Aargau",nameof(partnerCountries.Aargau) ,partnerCountries.Aargau),
                        ("Appenzell Ausserrhoden",nameof(partnerCountries.AppenzellAusserrhoden) ,partnerCountries.AppenzellAusserrhoden),
                        ("Appenzell Innerrhoden",nameof(partnerCountries.AppenzellInnerrhoden) ,partnerCountries.AppenzellInnerrhoden),
                        ("Basel-Landschaft",nameof(partnerCountries.Basel_Landschaft) ,partnerCountries.Basel_Landschaft),
                        ("Basel-Stadt",nameof(partnerCountries.Basel_Stadt) ,partnerCountries.Basel_Stadt),
                        ("Bern",nameof(partnerCountries.Bern) ,partnerCountries.Bern),
                        ("Freiburg (Fribourg)",nameof(partnerCountries.Freiburg) ,partnerCountries.Freiburg),
                        ("Genf (Genève)",nameof(partnerCountries.Genf) ,partnerCountries.Genf),
                        ("Glarus",nameof(partnerCountries.Glarus) ,partnerCountries.Glarus),
                        ("Graubünden (Grisons)",nameof(partnerCountries.Graubünden) ,partnerCountries.Graubünden),
                        ("Jura",nameof(partnerCountries.Jura) ,partnerCountries.Jura),
                        ("Luzern",nameof(partnerCountries.Luzern) ,partnerCountries.Luzern),
                        ("Neuenburg (Neuchâtel)",nameof(partnerCountries.Neuenburg) ,partnerCountries.Neuenburg),
                        ("Nidwalden",nameof(partnerCountries.Nidwalden) ,partnerCountries.Nidwalden),
                        ("Obwalden",nameof(partnerCountries.Obwalden) ,partnerCountries.Obwalden),
                        ("Schaffhausen",nameof(partnerCountries.Schaffhausen) ,partnerCountries.Schaffhausen),
                        ("Schwyz",nameof(partnerCountries.Schwyz) ,partnerCountries.Schwyz),
                        ("Solothurn",nameof(partnerCountries.Solothurn) ,partnerCountries.Solothurn),
                        ("St. Gallen",nameof(partnerCountries.St_Gallen) ,partnerCountries.St_Gallen),
                        ("Tessin (Ticino)",nameof(partnerCountries.Tessin) ,partnerCountries.Tessin),
                        ("Thurgau",nameof(partnerCountries.Thurgau) ,partnerCountries.Thurgau),
                        ("Uri",nameof(partnerCountries.Uri) ,partnerCountries.Uri),
                        ("Waadt (Vaud)",nameof(partnerCountries.Waadt) ,partnerCountries.Waadt),
                        ("Wallis (Valais)",nameof(partnerCountries.Wallis) ,partnerCountries.Wallis),
                        ("Zug",nameof(partnerCountries.Zug) ,partnerCountries.Zug),
                        ("Zürich",nameof(partnerCountries.Zürich) ,partnerCountries.Zürich),
                        ("Liechtenstein",nameof(partnerCountries.Lichtenstein) ,partnerCountries.Lichtenstein)
                    }
                },
                new CountryGroup
                {
                    LandCode = "AT",
                    LandName = "Austria",
                    Countries = new List<(string DisplayName,string Name, bool Value)>
                    {
                        ("Burgenland",nameof(partnerCountries.Burgenland) ,partnerCountries.Burgenland),
                        ("Kärnten",nameof(partnerCountries.Kärnten) ,partnerCountries.Kärnten),
                        ("Niederösterreich",nameof(partnerCountries.Niederösterreich) ,partnerCountries.Niederösterreich),
                        ("Oberösterreich",nameof(partnerCountries.Oberösterreich) ,partnerCountries.Oberösterreich),
                        ("Salzburg",nameof(partnerCountries.Salzburg) ,partnerCountries.Salzburg),
                        ("Steiermark",nameof(partnerCountries.Steiermark) ,partnerCountries.Steiermark),
                        ("Tirol",nameof(partnerCountries.Tirol) ,partnerCountries.Tirol),
                        ("Vorarlberg",nameof(partnerCountries.Vorarlberg) ,partnerCountries.Vorarlberg),
                        ("Wien",nameof(partnerCountries.Wien) ,partnerCountries.Wien)
                    }
                },
                new CountryGroup
                {
                    LandCode = "DE",
                    LandName = "Germany",
                    Countries = new List<(string DisplayName,string Name, bool Value)>
                    {
                        ("Baden-Württemberg",nameof(partnerCountries.Baden_Württemberg) ,partnerCountries.Baden_Württemberg),
                        ("Bayern",nameof(partnerCountries.Bayern) ,partnerCountries.Bayern),
                        ("Berlin",nameof(partnerCountries.Berlin) ,partnerCountries.Berlin),
                        ("Brandenburg",nameof(partnerCountries.Brandenburg) ,partnerCountries.Brandenburg),
                        ("Bremen",nameof(partnerCountries.Bremen) ,partnerCountries.Bremen),
                        ("Hamburg",nameof(partnerCountries.Hamburg) ,partnerCountries.Hamburg),
                        ("Hessen",nameof(partnerCountries.Hessen) ,partnerCountries.Hessen),
                        ("Mecklenburg-Vorpommern",nameof(partnerCountries.Mecklenburg_Vorpommern) ,partnerCountries.Mecklenburg_Vorpommern),
                        ("Niedersachsen",nameof(partnerCountries.Niedersachsen) ,partnerCountries.Niedersachsen),
                        ("Nordrhein-Westfalen",nameof(partnerCountries.Nordrhein_Westfalen) ,partnerCountries.Nordrhein_Westfalen),
                        ("Rheinland-Pfalz",nameof(partnerCountries.Rheinland_Pfalz) ,partnerCountries.Rheinland_Pfalz),
                        ("Saarland",nameof(partnerCountries.Saarland) ,partnerCountries.Saarland),
                        ("Sachsen",nameof(partnerCountries.Sachsen) ,partnerCountries.Sachsen),
                        ("Sachsen-Anhalt",nameof(partnerCountries.Sachsen_Anhalt) ,partnerCountries.Sachsen_Anhalt),
                        ("Schleswig-Holstein",nameof(partnerCountries.Schleswig_Holstein) ,partnerCountries.Schleswig_Holstein),
                        ("Thüringen",nameof(partnerCountries.Thüringen) ,partnerCountries.Thüringen)
                    }
                }
            };


            return countryGroups;
        }

        #endregion
    }

    public class CheckoutRequestModel
    {
        public int ActivityId { get; set; }
    }
}
